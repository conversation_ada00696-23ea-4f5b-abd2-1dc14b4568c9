# SRT Subtitle Implementation

This document describes the implementation of proper SRT subtitle support to replace the mock 8-second timestamps with accurate timing based on actual audio duration.

## Problem Statement

Previously, the system used mock timestamps with 8-second intervals for all podcast subtitles, regardless of actual speech timing. This resulted in poor synchronization between audio and displayed text.

## Solution Overview

The new implementation:

1. **Backend**: Generates SRT files with accurate timing based on actual audio segment durations
2. **Frontend**: Parses SRT content and displays properly synchronized subtitles
3. **Backward Compatibility**: Supports legacy transcript formats while preferring SRT data

## Files Modified/Added

### Backend Changes

#### New Files:
- `python_backend/utils/srt_generator.py` - SRT generation utility
- `python_backend/requirements.txt` - Added `mutagen==1.47.0` for audio duration detection

#### Modified Files:
- `python_backend/main.py` - Updated podcast generation to create SRT content
- `python_backend/services/elevenlabs_service.py` - Added audio duration detection

### Frontend Changes

#### New Files:
- `src/utils/srtParser.js` - SRT parsing and conversion utilities
- `src/components/SRTTest.jsx` - Test component for SRT functionality

#### Modified Files:
- `src/Dashboard.jsx` - Updated transcript handling to use SRT parser

## Technical Implementation

### Backend SRT Generation

1. **Audio Duration Detection**: Uses `mutagen` library to get precise MP3 duration
2. **Timing Calculation**: Calculates start/end times based on actual audio lengths
3. **SRT Format**: Generates standard SRT format with proper timing
4. **Fallback**: Estimates duration based on word count if audio analysis fails

### Frontend SRT Processing

1. **SRT Parser**: Parses SRT content into usable transcript objects
2. **Legacy Support**: Converts old transcript formats to SRT-compatible structure
3. **Real-time Sync**: Uses accurate timing for subtitle highlighting during playback

## Data Structure Changes

### Podcast Metadata (Backend)

```json
{
  "id": "podcast_id",
  "title": "Market Update - Date",
  "audioUrl": "https://...",
  "transcript": [...],        // Array of timed segments (NEW)
  "rawScript": "...",        // Original script text
  "srtContent": "...",       // SRT formatted content (NEW)
  "duration": "10",
  "stocks": ["AAPL", "TSLA"],
  "createdAt": "2024-01-01T00:00:00",
  "status": "completed"
}
```

### Transcript Segment Format

```javascript
{
  id: 1,                    // Subtitle number
  start: 0.0,              // Start time in seconds
  end: 5.2,                // End time in seconds  
  text: "Subtitle text"    // Display text
}
```

## Installation

1. Install backend dependencies:
   ```bash
   ./install_dependencies.sh
   ```

2. Or manually:
   ```bash
   cd python_backend
   pip install mutagen==1.47.0
   ```

## Usage

### Generating New Podcasts

New podcasts will automatically include SRT timing:

1. Start backend: `cd python_backend && python main.py`
2. Start frontend: `npm run dev`
3. Generate a podcast through the UI
4. Subtitles will now sync accurately with audio

### Testing SRT Functionality

Use the test component to verify SRT parsing:

```javascript
import SRTTest from './components/SRTTest';

// Add to any component for testing
<SRTTest />
```

## Backward Compatibility

The system maintains compatibility with existing podcasts:

- **Array Format**: Already properly formatted transcripts work unchanged
- **String Format**: Legacy text transcripts are converted using sentence splitting
- **SRT Format**: New SRT content is parsed for optimal timing
- **Fallback**: Mock data is used if no transcript is available

## API Changes

### Backend Response

The `/generate-podcast` endpoint now returns:

```json
{
  "success": true,
  "podcastId": "...",
  "audioUrl": "...",
  "transcript": [...],     // Timed transcript array
  "srtContent": "..."      // SRT formatted content
}
```

### Frontend Processing

The frontend automatically detects and processes:

1. SRT content (preferred)
2. Transcript arrays (direct use)
3. Legacy text (converted)
4. Mock data (fallback)

## Performance Considerations

- **Audio Analysis**: Adds ~1-2 seconds per audio segment for duration detection
- **Memory Usage**: SRT content is stored alongside transcript arrays
- **Parsing**: Frontend SRT parsing is lightweight and cached

## Error Handling

- **Audio Duration Failure**: Falls back to text-based estimation
- **SRT Parse Failure**: Falls back to legacy transcript conversion
- **Missing Data**: Uses mock transcript as final fallback

## Future Enhancements

1. **Speaker Identification**: Add speaker labels to SRT content
2. **Word-level Timing**: More granular timing for individual words
3. **SRT Export**: Allow users to download SRT files
4. **Custom Timing**: Manual timing adjustment interface

## Troubleshooting

### Common Issues

1. **Missing mutagen**: Install with `pip install mutagen==1.47.0`
2. **Audio duration errors**: Check audio file format and integrity
3. **SRT parsing errors**: Validate SRT format with test component
4. **Timing sync issues**: Verify audio playback rate and browser compatibility

### Debug Mode

Enable debug logging in backend:

```python
logging.getLogger().setLevel(logging.DEBUG)
```

### Testing

Run the SRT test component to verify functionality:

```bash
# In browser console after loading SRTTest component
# Check for any parsing errors or timing issues
```

## Migration Guide

### For Existing Podcasts

Existing podcasts will continue to work with legacy timing. To upgrade:

1. Regenerate podcasts to get SRT timing
2. Or manually convert using the conversion utilities

### For Developers

Update any custom transcript processing to use the new SRT utilities:

```javascript
import { parseSRT, convertLegacyTranscript } from './utils/srtParser';

// Replace manual parsing with:
const transcriptData = convertLegacyTranscript(legacyData);
```
