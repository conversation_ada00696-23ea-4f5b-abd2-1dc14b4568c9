# Firebase Setup Instructions for LOBO AI

## ✅ Your Firebase Project is Ready!

Your Firebase project "loboai" is already created. Now you need to get the **web app configuration**.

## 🔧 Get Web App Configuration (REQUIRED)

**IMPORTANT**: You have Admin SDK credentials, but you need Web SDK config for React.

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your **"loboai"** project
3. Click the **gear icon** (Project Settings)
4. Scroll down to **"Your apps"** section
5. If you see a web app already, click on it. If not:
   - Click **"Add app"** → Select **Web** (</> icon)
   - Register app with nickname: **"LOBO AI Web"**
6. **Copy the config object** that looks like this:

```javascript
const firebaseConfig = {
  apiKey: "AIza...",
  authDomain: "loboai.firebaseapp.com",
  databaseURL: "https://loboai-default-rtdb.firebaseio.com/",
  projectId: "loboai",
  storageBucket: "loboai.appspot.com",
  messagingSenderId: "...",
  appId: "..."
};
```

## 🔐 Enable Authentication

1. In Firebase Console, go to **Authentication** > **Sign-in method**
2. Click on **Google** provider
3. **Enable it** and add your authorized domains:
   - `localhost` (for development)
   - Your production domain (when you deploy)
4. Save the configuration

## 📊 Set up Realtime Database

1. Go to **Realtime Database** in Firebase console
2. Click **"Create Database"**
3. Choose your location (preferably close to your users)
4. Start in **test mode** for now
5. Your database URL should be: `https://loboai-default-rtdb.firebaseio.com/`

## ⚡ Update Firebase Configuration

Replace the placeholder values in `src/firebase_util.js` with your actual config:

```javascript
const firebaseConfig = {
  apiKey: "YOUR_ACTUAL_API_KEY", // Replace this
  authDomain: "loboai.firebaseapp.com", // ✅ Already correct
  databaseURL: "https://loboai-default-rtdb.firebaseio.com/", // ✅ Already correct
  projectId: "loboai", // ✅ Already correct
  storageBucket: "loboai.appspot.com", // ✅ Already correct
  messagingSenderId: "YOUR_ACTUAL_SENDER_ID", // Replace this
  appId: "YOUR_ACTUAL_APP_ID" // Replace this
};
```

**Only replace the 3 placeholder values**: `apiKey`, `messagingSenderId`, and `appId`

## 6. Database Structure

The app will create the following structure in your Realtime Database:

```
users/
  {userId}/
    displayName: "User Name"
    email: "<EMAIL>"
    createdAt: "2024-01-01T00:00:00.000Z"
    profile/
      selectedStocks: ["AAPL", "TSLA", "GOOGL"]
      complexity: "intermediate"
      duration: "10"
      tone: "casual"
      timezone: "America/New_York"
      updatedAt: "2024-01-01T00:00:00.000Z"
    podcasts/
      {podcastId}/
        id: "podcastId"
        title: "Daily Stock Update - Jan 1, 2024"
        audioUrl: "https://example.com/audio.mp3"
        transcript: [...]
        createdAt: "2024-01-01T00:00:00.000Z"
```

## 7. Security Rules (Optional)

For production, update your Realtime Database rules:

```json
{
  "rules": {
    "users": {
      "$uid": {
        ".read": "$uid === auth.uid",
        ".write": "$uid === auth.uid"
      }
    }
  }
}
```

## 8. Run the Application

```bash
npm run dev
```

Your LOBO AI app should now be running with Firebase authentication and database integration!

## Troubleshooting

- Make sure your Firebase project has billing enabled if you plan to use it in production
- Check that your domain is added to the authorized domains in Firebase Authentication
- Verify that your Firebase configuration is correctly copied to `firebase_util.js`
- Check the browser console for any Firebase-related errors
