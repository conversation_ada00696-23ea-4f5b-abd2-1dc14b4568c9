/**
 * SRT (SubRip Subtitle) Parser Utility
 * Parses SRT format subtitle files and converts them to usable transcript data
 */

/**
 * Parse SRT content into an array of subtitle objects
 * @param {string} srtContent - Raw SRT file content
 * @returns {Array} Array of subtitle objects with start, end, and text properties
 */
export function parseSRT(srtContent) {
  if (!srtContent || typeof srtContent !== 'string') {
    console.warn('Invalid SRT content provided');
    return [];
  }

  // Split by double newlines to separate subtitle blocks
  const blocks = srtContent.trim().split(/\n\s*\n/);
  const subtitles = [];

  for (const block of blocks) {
    const lines = block.trim().split('\n');
    
    // Skip empty blocks
    if (lines.length < 3) continue;

    // Parse subtitle number (first line)
    const subtitleNumber = parseInt(lines[0].trim());
    if (isNaN(subtitleNumber)) continue;

    // Parse timing (second line)
    const timingLine = lines[1].trim();
    const timingMatch = timingLine.match(/(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})/);
    
    if (!timingMatch) {
      console.warn(`Invalid timing format in subtitle ${subtitleNumber}: ${timingLine}`);
      continue;
    }

    const startTime = parseTimeToSeconds(timingMatch[1]);
    const endTime = parseTimeToSeconds(timingMatch[2]);

    // Parse text (remaining lines)
    const text = lines.slice(2).join(' ').trim();

    if (text && startTime !== null && endTime !== null) {
      subtitles.push({
        id: subtitleNumber,
        start: startTime,
        end: endTime,
        text: text
      });
    }
  }

  return subtitles;
}

/**
 * Convert SRT time format (HH:MM:SS,mmm) to seconds
 * @param {string} timeString - Time in SRT format
 * @returns {number|null} Time in seconds or null if invalid
 */
function parseTimeToSeconds(timeString) {
  if (!timeString) return null;

  const match = timeString.match(/(\d{2}):(\d{2}):(\d{2}),(\d{3})/);
  if (!match) return null;

  const hours = parseInt(match[1]);
  const minutes = parseInt(match[2]);
  const seconds = parseInt(match[3]);
  const milliseconds = parseInt(match[4]);

  return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000;
}

/**
 * Convert seconds to SRT time format (HH:MM:SS,mmm)
 * @param {number} seconds - Time in seconds
 * @returns {string} Time in SRT format
 */
export function secondsToSRTTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
}

/**
 * Generate SRT content from transcript array
 * @param {Array} transcriptArray - Array of transcript objects with start, end, text
 * @returns {string} SRT formatted content
 */
export function generateSRT(transcriptArray) {
  if (!Array.isArray(transcriptArray)) {
    return '';
  }

  return transcriptArray.map((segment, index) => {
    const subtitleNumber = index + 1;
    const startTime = secondsToSRTTime(segment.start);
    const endTime = secondsToSRTTime(segment.end);
    
    return `${subtitleNumber}\n${startTime} --> ${endTime}\n${segment.text}\n`;
  }).join('\n');
}

/**
 * Validate SRT content format
 * @param {string} srtContent - Raw SRT content
 * @returns {boolean} True if valid SRT format
 */
export function validateSRT(srtContent) {
  if (!srtContent || typeof srtContent !== 'string') {
    return false;
  }

  // Check for basic SRT structure
  const blocks = srtContent.trim().split(/\n\s*\n/);
  
  for (const block of blocks) {
    const lines = block.trim().split('\n');
    
    if (lines.length < 3) continue;
    
    // Check subtitle number
    const subtitleNumber = parseInt(lines[0].trim());
    if (isNaN(subtitleNumber)) return false;
    
    // Check timing format
    const timingMatch = lines[1].trim().match(/\d{2}:\d{2}:\d{2},\d{3}\s*-->\s*\d{2}:\d{2}:\d{2},\d{3}/);
    if (!timingMatch) return false;
  }

  return true;
}

/**
 * Find the current subtitle segment based on current time
 * @param {Array} subtitles - Array of subtitle objects
 * @param {number} currentTime - Current playback time in seconds
 * @returns {Object|null} Current subtitle object or null
 */
export function getCurrentSubtitle(subtitles, currentTime) {
  if (!Array.isArray(subtitles) || typeof currentTime !== 'number') {
    return null;
  }

  return subtitles.find(subtitle => 
    currentTime >= subtitle.start && currentTime <= subtitle.end
  ) || null;
}

/**
 * Convert legacy transcript format to SRT-compatible format
 * @param {string|Array} transcript - Legacy transcript data
 * @returns {Array} Array of subtitle objects
 */
export function convertLegacyTranscript(transcript) {
  if (Array.isArray(transcript)) {
    // Already in array format, just ensure proper structure
    return transcript.map((segment, index) => ({
      id: index + 1,
      start: segment.start || 0,
      end: segment.end || 0,
      text: segment.text || ''
    }));
  }

  if (typeof transcript === 'string') {
    // Check if it's SRT format
    if (validateSRT(transcript)) {
      return parseSRT(transcript);
    }

    // Otherwise, split into sentences with mock timing
    const sentences = transcript.split(/[.!?]+/).filter(s => s.trim().length > 0);
    return sentences.map((sentence, index) => ({
      id: index + 1,
      start: index * 8,
      end: (index + 1) * 8,
      text: sentence.trim() + '.'
    }));
  }

  return [];
}
