import React, { useState } from 'react';
import { parseSRT, generateSRT, validateSRT, convertLegacyTranscript } from '../utils/srtParser';

const SRTTest = () => {
  const [testResults, setTestResults] = useState([]);

  const runTests = () => {
    const results = [];

    // Test 1: Parse valid SRT content
    const sampleSRT = `1
00:00:00,000 --> 00:00:05,000
Good morning! Welcome to your personalized stock update for today.

2
00:00:05,000 --> 00:00:12,000
Let's start with Apple, which closed up 2.3% yesterday at $185.42.

3
00:00:12,000 --> 00:00:20,000
The company announced strong quarterly earnings, beating analyst expectations.`;

    try {
      const parsed = parseSRT(sampleSRT);
      results.push({
        test: 'Parse SRT',
        success: parsed.length === 3,
        details: `Parsed ${parsed.length} segments`
      });
    } catch (error) {
      results.push({
        test: 'Parse SRT',
        success: false,
        details: error.message
      });
    }

    // Test 2: Validate SRT format
    try {
      const isValid = validateSRT(sampleSRT);
      results.push({
        test: 'Validate SRT',
        success: isValid,
        details: isValid ? 'Valid SRT format' : 'Invalid SRT format'
      });
    } catch (error) {
      results.push({
        test: 'Validate SRT',
        success: false,
        details: error.message
      });
    }

    // Test 3: Generate SRT from transcript array
    try {
      const transcriptArray = [
        { start: 0, end: 5, text: "Test segment 1" },
        { start: 5, end: 10, text: "Test segment 2" }
      ];
      const generated = generateSRT(transcriptArray);
      const isValidGenerated = validateSRT(generated);
      results.push({
        test: 'Generate SRT',
        success: isValidGenerated,
        details: isValidGenerated ? 'Generated valid SRT' : 'Generated invalid SRT'
      });
    } catch (error) {
      results.push({
        test: 'Generate SRT',
        success: false,
        details: error.message
      });
    }

    // Test 4: Convert legacy transcript
    try {
      const legacyText = "This is a test sentence. This is another sentence! And a third one?";
      const converted = convertLegacyTranscript(legacyText);
      results.push({
        test: 'Convert Legacy',
        success: converted.length === 3,
        details: `Converted to ${converted.length} segments`
      });
    } catch (error) {
      results.push({
        test: 'Convert Legacy',
        success: false,
        details: error.message
      });
    }

    setTestResults(results);
  };

  return (
    <div className="p-6 bg-gray-100 rounded-lg">
      <h3 className="text-lg font-bold mb-4">SRT Parser Test Suite</h3>
      
      <button
        onClick={runTests}
        className="mb-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Run Tests
      </button>

      {testResults.length > 0 && (
        <div className="space-y-2">
          {testResults.map((result, index) => (
            <div
              key={index}
              className={`p-3 rounded ${
                result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}
            >
              <div className="font-semibold">{result.test}</div>
              <div className="text-sm">{result.details}</div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SRTTest;
