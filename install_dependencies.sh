#!/bin/bash

echo "Installing backend dependencies for enhanced stock data and news integration..."

cd python_backend

# Install the new dependencies
echo "Installing yfinance for stock data..."
pip install yfinance==0.2.28

echo "Installing tenacity for retry logic..."
pip install tenacity==8.2.3

echo "Installing mutagen for SRT support..."
pip install mutagen==1.47.0

echo "Installing all requirements..."
pip install -r requirements.txt

echo ""
echo "Dependencies installed successfully!"
echo ""
echo "The following integrations have been added:"
echo "1. 📈 yfinance - Primary stock data source (free, no API key required)"
echo "2. 📰 Perplexity AI - Advanced news service for stock news"
echo "3. 🎵 SRT subtitle generation with proper timing"
echo "4. 🔄 Improved retry logic and error handling"
echo ""
echo "To test the new integrations:"
echo "1. Set up your .env file with API keys (see python_backend/.env.example)"
echo "2. Run integration tests: cd python_backend && python test_integrations.py"
echo "3. Start the backend: cd python_backend && python main.py"
echo "4. Start the frontend: npm run dev"
echo ""
echo "Note: yfinance works without API keys, but Perplexity requires PERPLEXITY_API_KEY"
