# Stock Data & News Integration

This document describes the integration of yfinance for stock data and Perplexity AI for stock news in the LOBO AI podcast system.

## Overview

The system now uses two primary services for financial data:

1. **📈 yfinance** - Primary stock data source (prices, changes, market data)
2. **📰 Perplexity AI** - Advanced news service for real-time stock news

## Stock Data Integration (yfinance)

### Features

- **Real-time stock prices** with accurate market data
- **No API key required** - completely free to use
- **Comprehensive data** including:
  - Current price and daily change
  - 52-week high/low
  - Market capitalization
  - P/E ratio, dividend yield, beta
  - Trading volume
- **Automatic fallback** to other APIs if needed

### Implementation

The `StockService` class now uses yfinance as the primary data source:

```python
# Primary data source
await stock_service._get_yfinance_data("AAPL")

# Fallback hierarchy:
# 1. yfinance (primary)
# 2. Alpha Vantage (if API key available)
# 3. Finnhub (if API key available)
# 4. Yahoo Finance API
# 5. Mock data (last resort)
```

### Benefits

- **Free and reliable** - No API limits or costs
- **Rich data** - More comprehensive than many paid APIs
- **Fast response** - Direct access to Yahoo Finance data
- **No rate limiting** - Suitable for production use

## News Integration (Perplexity AI)

### Features

- **Real-time news** from authoritative financial sources
- **Time-aware filtering** to prevent duplicate content
- **Source citations** with URLs and publication dates
- **Intelligent summarization** of news content
- **Timezone support** for accurate time filtering

### News Sources

The system searches across premium financial sources:
- Bloomberg
- Reuters
- CNBC
- MarketWatch
- Yahoo Finance
- Wall Street Journal
- SEC filings

### Implementation

```python
# Get news for last 24 hours
news_result = await news_service.get_stock_news_last_24h("AAPL")

# Get news for custom time range
news_result = await news_service.get_stock_news(
    symbol="AAPL",
    from_datetime=start_time,
    to_datetime=end_time,
    user_timezone="America/New_York"
)
```

### Time-Aware News Filtering

The system prevents duplicate content by:
- **Precise time filtering** - Only news within specified time windows
- **Timezone awareness** - Respects user's local timezone
- **Recency filters** - Optimizes search based on time range
- **Duplicate prevention** - Avoids repeating news across podcast generations

## Configuration

### Required Environment Variables

```bash
# Required for news service
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# Optional for enhanced stock data
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
FINNHUB_API_KEY=your_finnhub_key_here
```

### Getting API Keys

#### Perplexity AI (Required for News)
1. Visit [Perplexity AI](https://www.perplexity.ai/)
2. Sign up for an account
3. Navigate to API settings
4. Generate an API key
5. Add to your `.env` file

#### Optional Stock APIs
- **Alpha Vantage**: [Get free key](https://www.alphavantage.co/support/#api-key)
- **Finnhub**: [Get free key](https://finnhub.io/)

## Testing

Run the integration test script to verify everything works:

```bash
cd python_backend
python test_integrations.py
```

This will test:
- ✅ yfinance stock data fetching
- ✅ Perplexity news service
- ✅ Integrated stock service
- ✅ Service health checks

## Usage in Podcast Generation

The enhanced stock service is automatically used during podcast generation:

1. **Stock Data Collection**: yfinance fetches current prices and market data
2. **News Gathering**: Perplexity searches for recent news within the specified time window
3. **Content Integration**: Both data sources are combined into the podcast script
4. **Fallback Handling**: If any service fails, the system gracefully falls back to alternatives

## Error Handling

The system includes robust error handling:

- **Graceful degradation** - If Perplexity fails, falls back to Finnhub news
- **Retry logic** - Automatic retries with exponential backoff
- **Comprehensive logging** - Detailed error tracking for debugging
- **Health monitoring** - Real-time service status checking

## Performance Optimizations

- **Concurrent requests** - Multiple stocks fetched simultaneously
- **Async operations** - Non-blocking API calls
- **Intelligent caching** - Reduces redundant API calls
- **Timeout handling** - Prevents hanging requests

## Migration from Previous System

The new system is backward compatible:

- **Existing podcasts** continue to work
- **Gradual migration** - New podcasts use enhanced data
- **Fallback support** - Old APIs still available as backups
- **No breaking changes** - API responses maintain same structure

## Monitoring and Health

Check service health via the test endpoint:

```bash
# Test all integrations
python test_integrations.py

# Check specific service
curl http://localhost:8000/health
```

The health check returns status for:
- yfinance connectivity
- Perplexity API status
- Alpha Vantage (if configured)
- Finnhub (if configured)
- Yahoo Finance API

## Troubleshooting

### Common Issues

1. **Perplexity API errors**
   - Check API key is valid
   - Verify account has sufficient credits
   - Check network connectivity

2. **yfinance data issues**
   - Verify stock symbol is valid
   - Check if market is open
   - Try alternative symbols

3. **News not appearing**
   - Check time range settings
   - Verify timezone configuration
   - Review Perplexity response logs

### Debug Mode

Enable debug logging in your `.env`:

```bash
DEBUG=True
```

This provides detailed logs for troubleshooting integration issues.
